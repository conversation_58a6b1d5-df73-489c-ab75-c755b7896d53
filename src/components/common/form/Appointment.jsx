'use client'

import React, { useState, useRef } from 'react'
import { Cancel01Icon, File01Icon, CloudUploadIcon, CreditCardIcon, BankIcon, ArrowRight01Icon } from 'hugeicons-react'
import But<PERSON> from '../Button'

const FileUploadInput = ({ label, name, onChange }) => {
    const fileInputRef = useRef(null);
    const [fileName, setFileName] = useState('');

    const handleFileChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            setFileName(file.name);
            onChange(e);
        }
    };

    const handleClick = () => {
        fileInputRef.current.click();
    };

    return (
        <div className="form-group">
            <label>{label}</label>
            <div className="file-upload-container" onClick={handleClick}>
                <input
                    type="file"
                    ref={fileInputRef}
                    name={name}
                    onChange={handleFileChange}
                    className="file-input-hidden"
                />
                <div className="file-upload-box">
                    {fileName ? (
                        <div className="file-selected">
                            <File01Icon size={20} />
                            <span>{fileName}</span>
                        </div>
                    ) : (
                        <div className="file-placeholder">
                            <div className="upload-icon-container">

                          <CloudUploadIcon  size={20} color="#141B34" />
                            </div>
                            <span>Choose your file or drag & drop it here</span>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

const Appointment = ({ onClose }) => {
  const [step, setStep] = useState(1)
  const [formData, setFormData] = useState({
    // Personal Information
    name: '',
    email: '',
    phone: '',
    educationLevel: '',
    idFile: null,
    ssFile: null,
    immunizationFile: null,
    diplomaFile: null,

    // Payment Information
    paymentMethod: '',
    termsAgreement: false
  })

  const handleChange = (e) => {
    const { name, value, type, checked, files } = e.target
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : type === 'file' ? files[0] : value
    })
  }

  const nextStep = (e) => {
    e.preventDefault()
    setStep(2)
  }

  const prevStep = (e) => {
    e.preventDefault()
    setStep(1)
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    console.log('Form submitted:', formData)
    alert('Enrollment successful! We will contact you soon.')
    onClose && onClose()
  }

  const renderPersonalInfo = () => (
    <>
      <div className='half'>
        <div className='form-group'>
          <label>Names</label>
          <input
            type='text'
            name='name'
            value={formData.name}
            onChange={handleChange}
            placeholder='Enter your full name'
            required
          />
        </div>
        <div className='form-group'>
          <label>Email</label>
          <input
            type='email'
            name='email'
            value={formData.email}
            onChange={handleChange}
            placeholder='Enter your email'
            required
          />
        </div>
      </div>
      <div className='half'>
        <div className='form-group'>
          <label>Phone number</label>
          <input
            type='tel'
            name='phone'
            value={formData.phone}
            onChange={handleChange}
            placeholder='Enter your phone number'
            required
          />
        </div>
        <div className='form-group'>
          <label>Level of education</label>
          <select
            name='educationLevel'
            value={formData.educationLevel}
            onChange={handleChange}
            required
          >
            <option value=''>Select your education level</option>
            <option value='highSchool'>High School Diploma</option>
            <option value='ged'>GED</option>
            <option value='college'>College Degree</option>
          </select>
        </div>
      </div>
      <div className='half'>
        <FileUploadInput
          label="Valid Government-Issued ID"
          name="idFile"
          onChange={handleChange}
          placeholder="Upload your ID"
        />
        <FileUploadInput
          label="Social Security Card"
          name="ssFile"
          onChange={handleChange}
          placeholder="Upload your SSC"
        />
      </div>
      <div className='half'>
        <FileUploadInput
          label="Immunization Records"
          name="immunizationFile"
          onChange={handleChange}
          placeholder="Upload your records"
        />
        <FileUploadInput
          label="High School Diploma/GED Certificate"
          name="diplomaFile"
          onChange={handleChange}
          placeholder="Upload your diploma"
        />
      </div>
    </>
  )


  const renderPaymentInfo = () => (
    <>
      <div className='payments'>
        <div className='fees'>
          <h3>Fees</h3>
          <h1>$499 <span>USD</span></h1>
        </div>
        <div className='options'>
          <h1>Payment Options</h1>
          <div className='card' onClick={() => setFormData({...formData, paymentMethod: 'creditCard'})}>
            <CreditCardIcon size={24} color="#3D40A8"  className='icon'/>
            <p>Credit Card</p>
          </div>
          <div className='card' onClick={() => setFormData({...formData, paymentMethod: 'paypal'})}>
            <img src="/paypal.svg" alt="paypal"  className='icon'/>
            <p>Paypal</p>
          </div>
          <div className='card' onClick={() => setFormData({...formData, paymentMethod: 'bankTransfer'})}>
            <BankIcon size={24} color="#3D40A8"  className='icon'/>
            <p>Bank Transfer</p>
          </div>
        </div>
      </div>

      {/* <div className='form-group'>
        <div className='checkbox-option'>
          <input
            type='checkbox'
            id='termsAgreement'
            name='termsAgreement'
            checked={formData.termsAgreement}
            onChange={handleChange}
          />
          <label htmlFor='termsAgreement'>I agree to the terms and conditions of the CNA Training Program</label>
        </div>
      </div> */}
    </>
  )



  return (
    <div className='appointment-form'>
      <div className='title'>
        <h3>Enroll Form</h3>
        <Cancel01Icon onClick={onClose} style={{ cursor: 'pointer' }} />
      </div>

      {/* <div className='progress-bar'>
        <div className='progress' style={{ width: `${step === 1 ? '50%' : '100%'}` }}></div>
      </div> */}

      <form onSubmit={step === 2 ? handleSubmit : nextStep}>
        {step === 1 ? renderPersonalInfo() : renderPaymentInfo()}

        <div className='actions'>
          {step > 1 && (
            <Button
              text="Back"
              onClick={prevStep}
              className="secondary"
              type="button"
            />
          )}

          {step === 1 ? (
            <Button
              text="Continue"
              icon={<ArrowRight01Icon size={16} />}
              onClick={nextStep}
              className="primary"
              type="button"
            />
          ) : (
            <Button
              text="Complete Enrollment"
              type="submit"
              className="primary"
            />
          )}
        </div>
      </form>
    </div>
  )
}

export default Appointment
