import React from 'react'
import { El<PERSON>sis, Smartphone, Mail } from 'lucide-react'
import But<PERSON> from '../Button'

const Contact = () => {
  return (
    <div id="contact-section" className='contact-container'>
      <img src="/curves-top.png" alt="" className="curves-top" />
      <div className='contact-wrapper'>
        <div className='contact-left'>
          <div className='contact-header'>
            <Ellipsis color="#39B54A" />
            <h5>Contact us</h5>
            <h1>Have questions or want to book <br /> your stay? We’re here to help.</h1>
            <p>Reach out using the form or via our contact details.</p>
          </div>

          <div className='contact-cards'>
            <div className='contact-card'>
              <Smartphone color="#2E3192" />
              <div className='card-text'>
                <h4>Phone Number</h4>
                <p>(*************</p>
              </div>
            </div>

            <div className='contact-card'>
              <Mail color="#2E3192" />
              <div className='card-text'>
                <h4>Email</h4>
                <p><EMAIL></p>
              </div>
            </div>
          </div>
        </div>

        <div className='contact-right'>
          <form>
              <input type='text' placeholder='Your name' />

              <input type='email' placeholder='<EMAIL>' />

              <textarea placeholder='Enter your message' rows="8" cols="50"></textarea>

            <Button className="primary" text="Send a message" />
          </form>
        </div>
      </div>
      {/* <img src="/curves-bottom.png" alt="" className="curves-bottom" /> */}
    </div>
  )
}

export default Contact
