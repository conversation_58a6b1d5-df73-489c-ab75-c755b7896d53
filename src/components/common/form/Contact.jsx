import React from 'react'
import { Ellipsis, Smartphone, Mail, MapPin } from 'lucide-react'
import But<PERSON> from '../Button'

const Contact = () => {
  return (
    <div id="contact-section" className='contact-container'>
      <img src="/curves-top.png" alt="" className="curves-top" />
      <div className='contact-wrapper'>
        <div className='contact-left'>
          <div className='contact-header'>
            <Ellipsis color="#39B54A" />
            <h5>Contact us</h5>
            <h1>Have questions or ready to enroll? We’re here to help.</h1>
            <p>Reach out using the form or via our contact details.</p>
          </div>

          <div className='contact-cards'>
            <div className='group-card'>
 <div className='contact-card'>
              <Smartphone color="#2E3192" />
              <div className='card-text'>
                <h4>Phone Number</h4>
                <p>(*************</p>
              </div>
            </div>

            <div className='contact-card'>
              <Mail color="#2E3192" />
              <div className='card-text'>
                <h4>Email</h4>
                <p><EMAIL></p>
              </div>
            </div>
            </div>
           
               <div className='contact-card'>
              <MapPin color="#2E3192" />
              <div className='card-text'>
                <h4>Address</h4>
                <p>43155 E 45th St, Shawnee, OK 74804</p>
              </div>
            </div>

          </div>
        </div>

        <div className='contact-right'>
          <form>
            <label htmlFor='name'>Full Name</label>
              <input type='text' placeholder='Your name' />
              <label htmlFor='email'>Email</label>

              <input type='email' placeholder='Your email' />
              <label htmlFor='message'>Message</label>

              <textarea placeholder='Enter your message' rows="8" cols="50"></textarea>

            <Button className="primary" text="Send a message" />
          </form>
        </div>
      </div>
      {/* <img src="/curves-bottom.png" alt="" className="curves-bottom" /> */}
    </div>
  )
}

export default Contact
