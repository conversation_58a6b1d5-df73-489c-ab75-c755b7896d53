'use client'

import React, { useState } from 'react'
import { Note01Icon, Cancel01Icon, CopyrightIcon } from '@hugeicons/react'
import EnrollButton from './EnrollButton'

const Footer = () => {
    const [activePopup, setActivePopup] = useState(null)

    const openPopup = (popupName) => {
        setActivePopup(popupName)
        document.body.style.overflow = 'hidden'
    }

    const closePopup = () => {
        setActivePopup(null)
        document.body.style.overflow = 'auto'
    }

    return (
        <>
            <div className="footer container">
                <div className='quick-links'>
                    <div className='logo-text'>
                        <a href='#'>
                            <img src={'/logo.svg'} alt="logo" />
                        </a>

                        <h2 className='footer-text'>Start Your Healthcare <br /> Journey Here.</h2>
                        <EnrollButton />

                    </div>
                    <div className='footer-links'>
                        <h3>Quick Links</h3>
                        <a href="#">Home</a>
                        <a href="#">About us</a>
                        <a href="#">Contact us</a>
                        <a href="#">Classes</a>
                    </div>
                    <div className='footer-links contacts'>
                        <h3>Contacts</h3>
                        <ul>
                            <li> <Note01Icon color="#2E3192" />(918) 869-0462</li>
                            <li> <Note01Icon color="#2E3192" /><EMAIL></li>
                            <li><Note01Icon color="#2E3192"/>43155 E 45th St, Shawnee, OK 74804</li>
                        </ul>
                    </div>
                    <div className='footer-links updates'>
                        <Note01Icon color="#2E3192" />
                        <h1>Apply for next cohort</h1>
                        <h3 className='date'>17 june 2025</h3>
                    </div>
                </div>

                <div className='footer-copyright'>
                    <div className='copyright'>
                    <CopyrightIcon size={18}  color="#5F5F5F"/>
                        <p> Cohesive Training Academy</p>
                    </div>
                    <div className='policies'>
                        <p onClick={() => openPopup('refund')} style={{ cursor: 'pointer' }}>Refund & Cancellation Policy</p>
                        <p onClick={() => openPopup('accreditation')} style={{ cursor: 'pointer' }}>Accreditation & Approval</p>
                    </div>
                </div>
            </div>

            {activePopup && (
                <div className="modal-overlay" onClick={closePopup}>
                    {activePopup === 'refund' && (
                        <div className='refund-popup' onClick={(e) => e.stopPropagation()}>
                             <div className='title'>
                                <h3>Refund & Cancellation Policy</h3>
                                <Cancel01Icon style={{ cursor: 'pointer' }} onClick={closePopup} />
                            </div>
                            <div className='contents'>

                            <div className="icon">
                                <img src="/refund-icon.png" alt="refund" />
                            </div>
                            <h4>Refund & Cancellation Policy</h4>
                            <p>All deposits are non-refundable. Students who withdraw after the start of class may be eligible for a partial refund, depending on the percentage of the program completed at the time of withdrawal. To request a refund, students must contact the Program Director in writing.</p>
                        </div>
                        </div>
                    )}

                    {activePopup === 'accreditation' && (
                        <div className='accreditation-popup' onClick={(e) => e.stopPropagation()}>
                             <div className='title'>
                                <h3>Accreditation & Approval</h3>
                                <Cancel01Icon style={{ cursor: 'pointer' }} onClick={closePopup} />
                            </div>
                            <div className='contents'>

                            <div className="icon">
                                <img src="/Accreditation.png" alt="accreditation" />
                            </div>
                            <h4>Accreditation & Approval</h4>
                            <p>Cohesive Healthcare Training Academy is officially approved by the Oklahoma State Department of Health as a Long-Term Care Nurse Aide Training and Competency Evaluation Program (NATCEP). Program Code: 3750122</p>
                        </div>
                        </div>
                    )}
                </div>
            )}
        </>
    )
}

export default Footer
