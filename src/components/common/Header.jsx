
'use client'

import React, { useState } from 'react'
import <PERSON><PERSON> from './Button'
import { ChevronRight, Menu, X } from 'lucide-react'
import Link from 'next/link'

const Header = () => {
    const [isMenuOpen, setIsMenuOpen] = useState(false)

    const toggleMenu = () => {
        setIsMenuOpen(!isMenuOpen)
    }

    const scrollToContact = (e) => {
        e.preventDefault()
        const contactSection = document.getElementById('contact-section')
        if (contactSection) {
            contactSection.scrollIntoView({ behavior: 'smooth' })
            setIsMenuOpen(false)
        }
    }

    return (
        <div className="navigation container">
            <div className='logo'>
                <Link href='/'>
                    <img src={'/logo.svg'} alt="logo" />
                </Link>
            </div>

            <div className='menu-toggle' onClick={toggleMenu}>
                {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </div>

            <div className={`nav-links ${isMenuOpen ? 'active' : ''}`}>
                <Link href="/" onClick={() => setIsMenuOpen(false)}>Home</Link>
                <Link href="/courses" onClick={() => setIsMenuOpen(false)}>Classes</Link>
                <Link href="#" onClick={() => setIsMenuOpen(false)}>Payment</Link>
                <Link href="#" onClick={() => setIsMenuOpen(false)}>Tuition help</Link>

                <div className='contact-us'>
                    <Button
                        text="Contact us"
                        icon={<ChevronRight size={16} />}
                        onClick={scrollToContact}
                    />
                </div>
            </div>
        </div>
    )
}

export default Header
