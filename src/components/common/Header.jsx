
'use client'

import React, { useState } from 'react'
import <PERSON>ton from './Button'
import { Menu01Icon, Cancel01Icon, ArrowRight01Icon } from 'hugeicons-react'
import Link from 'next/link'

const Header = () => {
    const [isMenuOpen, setIsMenuOpen] = useState(false)

    const toggleMenu = () => {
        setIsMenuOpen(!isMenuOpen)
    }

    const scrollToSection = (sectionId) => (e) => {
        e.preventDefault()
        const section = document.getElementById(sectionId)
        if (section) {
            section.scrollIntoView({ behavior: 'smooth' })
            setIsMenuOpen(false)
        }
    }

    const scrollToContact = scrollToSection('contact-section')

    return (
        <div className="navigation container">
            <div className='logo'>
                <Link href='/'>
                    <img src={'/logo.svg'} alt="logo" />
                </Link>
            </div>

            <div className='menu-toggle' onClick={toggleMenu}>
                {isMenuOpen ? <Cancel01Icon size={24} /> : <Menu01Icon size={24} />}
            </div>

            <div className={`nav-links ${isMenuOpen ? 'active' : ''}`}>
                <Link href="/" onClick={() => setIsMenuOpen(false)}>Home</Link>
                <Link href="#" onClick={scrollToSection('classes-section')}>Classes</Link>
                <Link href="#" onClick={scrollToSection('payment-section')}>Payment</Link>
                <Link href="#" onClick={scrollToSection('payment-section')}>Tuition help</Link>

                <div className='contact-us'>
                    <Button
                        text="Contact us"
                        icon={<ArrowRight01Icon size={16} />}
                        onClick={scrollToContact}
                    />
                </div>
            </div>
        </div>
    )
}

export default Header
