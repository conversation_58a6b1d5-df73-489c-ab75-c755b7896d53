'use client'

import React, { useState } from 'react'
import Button from './Button'
import { ArrowRight01Icon } from 'hugeicons-react'
import Appointment from './form/Appointment'

const EnrollButton = ({ text = "Enroll now", className = "primary", onClick }) => {
  const [isFormOpen, setIsFormOpen] = useState(false)

  const openForm = () => {
    if (onClick) onClick()

    setIsFormOpen(true)
    document.body.style.overflow = 'hidden'
  }

  const closeForm = () => {
    setIsFormOpen(false)
    document.body.style.overflow = 'auto'
  }

  return (
    <>
      <Button
        className={className}
        text={text}
        icon={<ArrowRight01Icon size={16} />}
        onClick={openForm}
      />

      {isFormOpen && (
        <div className="modal-overlay">
          <Appointment onClose={closeForm} />
        </div>
      )}
    </>
  )
}

export default EnrollButton
