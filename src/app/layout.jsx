import { Geist, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import Header from "@/components/common/Header";
import Footer from "@/components/common/Footer";
import './styles/components/_button.scss';
import './styles/components/_header.scss';
import './styles/components/_footer.scss';
import './styles/components/_contactForm.scss';
import './styles/pages/_homePage.scss';
import './styles/pages/_course.scss';
import './styles/components/_appointmentForm.scss';
import { Space_Grotesk } from 'next/font/google';

const spaceGrotesk = Space_Grotesk({
  subsets: ['latin'],
  weight: ['400', '500', '600', '700'],
  variable: '--font-space-grotesk',
  display: 'swap',
})

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  title: "Cohesive Training Academy",
  description: "Generated by create next app",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body className={`${spaceGrotesk.variable} ${geistSans.variable} ${geistMono.variable}`}>
        <Header />
       
          {children}
         
        <Footer />
      </body>
    </html>
  );
}
