:root {
  --background: #ffffff;
  --foreground: #171717;
  --font-avigea: 'Avigea', serif;
  --font-space-grotesk: 'Space Grotesk', sans-serif;
  --max-content-width: 1920px;
}

@font-face {
  font-family: 'Avigea';
  src: url('/fonts/Avigea.woff2') format('woff2'),
       url('/fonts/Avigea.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

/* @media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
} */

html,
body {
  max-width: 1600px;
  overflow-x: hidden;
  font-family: var(--font-space-grotesk), sans-serif;
  display: flex;
  flex-direction: column;
  margin: 0 auto;

}

body {
  color: var(--foreground);
  background: var(--background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  scroll-behavior: smooth;
}

/* Scroll offset for fixed header */
html {
  scroll-padding-top: 120px;
}

@media (max-width: 768px) {
  html {
    scroll-padding-top: 100px;
  }
}

@media (min-width: 769px) and (max-width: 834px) {
  html {
    scroll-padding-top: 110px;
  }
}

.container {
  width: 100%;
  /* padding: 24px 80px; */
  /* max-width: var(--max-content-width); */
  /* margin: 0 auto; */
}

.page-content {
  /* max-width: var(--max-content-width); */
  /* margin: 0 auto; */
  width: 100%;
}


.section {
  /* max-width: var(--max-content-width); */
  /* margin: 0 auto; */
  width: 100%;
}
@media screen and (max-width: 1440px) {
 html,
body {
padding: 20px;
  /* margin: 0 auto; */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

    
}
@media screen and (max-width: 768px) {
  .container {
    padding: 24px 20px;
  }
}



@media screen and (min-width: 1441px) {
  .container {
    padding: 24px;
  }
}

h1 {
  font-family: var(--font-avigea);
}

h2, h3, h4, h5, h6, p, span, a, button, input, textarea {
  font-family: var(--font-space-grotesk), sans-serif;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

a {
  color: inherit;
  text-decoration: none;
}
ul{
  list-style: none;
}
input, textarea, select{
  border: none;
  outline: none;
  background-color: #FAFAFA;
  color: #C7C6C6;
  padding: 16px;
  border-radius: 12px;

}
textarea{
  resize: none;
}


@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}

