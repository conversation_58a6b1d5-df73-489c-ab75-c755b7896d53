.home {
  display: flex;
  flex-direction: column;
  gap: 200px;
  padding: 66px 24px 24px;

  .hero {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 4rem;


    .left-section {
      display: flex;
      flex-direction: column;
      gap: 150px;


      .hero-text {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        padding-top: 100px;

        h1 {
          font-size: 56px;
        }

        h5 {
          color: #3D40A8;
          font-size: 18px;
        }

        p {
          font-size: 18px;
          line-height: 1.6;
          color: #555;
        }

        .highlight {
          color: #3D40A8;
        }



        .enrollment {
          display: flex;
          align-items: end;
          gap: 1rem;
          background-color: #F8F8F8;
          border-radius: 12px;
          padding: 20px;

          .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            flex: 1;

            .enrol-input {
              padding: 0;
              border-radius: 0;

            }

          }


        }
      }
    }

    .convenient {
      display: flex;
      align-items: center;
      gap: 4rem;
      border-radius: 12px;
      background-color: white;
      max-width: 100%;
      flex-wrap: wrap;

      .convenient-icon {
        position: relative;
        display: inline-block;
        width: fit-content;

        .location {
          background-color: #3D40A8;
          padding: 12px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .arrow {
          position: absolute;
          top: 50%;
          left: 122%;
          transform: translate(-50%, -50%);
          background-color: white;
          border-radius: 12px;
          padding: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 2;
        }
      }

      .convenient-text {
        font-weight: 700;
        color: #000;
        white-space: nowrap;



      }


    }



    .hero-img {
      position: relative;
      width: 100%;
      height: auto;

      img {
        width: 100%;
        height: 725px;
        object-fit: cover;
        border-radius: 12px;
      }

      .convenient {
        position: absolute;
        bottom: 1rem;
        right: 1rem;
        display: flex;
        align-items: center;
        gap: 4rem;
        background-color: white;
        padding: 0.75rem 1rem;
        border-radius: 12px;
        max-width: 90%;
        flex-wrap: wrap;
        z-index: 10;

        .convenient-icon {
          position: relative;
          display: inline-block;
          width: fit-content;

          .location {
            background-color: #2E3192;
            padding: 12px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .arrow {
            position: absolute;
            top: 50%;
            left: 122%;
            transform: translate(-50%, -50%);
            background-color: white;
            border-radius: 12px;
            padding: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
          }
        }

        .convenient-text {
          font-weight: 700;
          color: #000;
          display: flex;
          flex-direction: column;
          gap: 0.5rem;


          .date {
            color: #3D40A8;

          }
        }
      }
    }

  }


  .about-trainings {
    width: 100%;
    position: relative;
    padding: 42px 42px;
    border-radius: 32px;
    background-color: #2E3192;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;


    .curves-top {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 1;
      width: 50%;
      max-width: 600px;
      height: auto;
      object-fit: contain;
      pointer-events: none;
    }

    .curves-bottom {
      position: absolute;
      bottom: 0;
      left: 0;
      z-index: 1;
      width: 50%;
      max-width: 600px;
      height: auto;
      object-fit: contain;
      pointer-events: none;
    }

    .white-card {

      display: flex;
      justify-content: space-between;
      gap: 48px;
      align-items: center;
      background-color: white;
      justify-content: center;
      padding: 48px;
      border-radius: 24px;
      position: relative;
      z-index: 2;



      .about-trainings-text {
        flex-direction: column;
        gap: 24px;
        display: flex;
        width: 70%;

        .dots {
          display: flex;
          gap: 8px;
        }

        h5 {
          color: #3D40A8;
          font-size: 16px;
          font-weight: 600;
        }

        h1 {
          font-family: var(--font-avigea);
          color: #000;
          font-size: 28px;
          font-weight: 700;
        }

        p {
          font-family: var(--font-space-grotesk), sans-serif;
          color: #555;
          font-size: 16px;
          line-height: 1.6;
        }
      }

      .about-trainings-img {
        img {
          width: 100%;
          height: 600px;
          object-fit: cover;
          border-radius: 16px;
        }
      }
    }

    .class-section {
      display: flex;
      flex-direction: column;
      gap: 42px;
      padding: 0 60px 0;
      position: relative;
      z-index: 2;
      width: 100%;
      justify-content: center;
      align-items: center;

      .section-header {
        display: flex;
        flex-direction: column;
        gap: 16px;
        text-align: center;
        align-items: center;

        .dots.green {
          color: #39B54A;
        }

        h5 {
          font-family: var(--font-space-grotesk), sans-serif;
          color: #39B54A;
          font-size: 18px;
          font-weight: 500;
        }

        h2 {
          font-family: var(--font-avigea);
          color: white;
          font-size: 32px;
          font-weight: 700;
          text-align: center;
        }
      }

      .class-cards {
        display: flex;
        flex-direction: column;
        gap: 16px;
        width: 80%;
        max-width: 900px;

        .card-row {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 16px;
        }

        .class-card {
          background-color: white;
          padding: 24px;
          border-radius: 16px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          &.full-width {
            grid-column: span 2;
          }

          .card-content {
            display: flex;
            flex-direction: column;
            gap: 8px;

            p {
              font-family: var(--font-space-grotesk), sans-serif;
              color: #666;
              font-size: 14px;

            }

            h3 {
              font-family: var(--font-space-grotesk), sans-serif;
              color: black;
              font-size: 16px;
              font-weight: 600;

            }
          }

          .card-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
          }
        }
      }

      .schedule-note {
        color: white;
        text-align: center;
        font-size: 16px;
      }
    }
  }

  .How-to-apply {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    align-items: center;

    .apply-text {
      display: flex;
      gap: 4rem;
      align-items: center;

      .left {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 1rem;
      }

      .right {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 1rem;
      }

      h5 {
        color: #3D40A8;
      }

      h1 {
        color: black;
      }

      p {
        color: #000;
      }

      .card {
        background-color: #FCFCFC;
        border-radius: 16px;
        padding: 12px 24px;
        border: 1px solid #F7F7F7;

        p {
          color: #000;
        }
      }
    }

    .How-to-apply {
      display: flex;
      flex-direction: column;
      gap: 2rem;
      align-items: center;

      .apply-text {
        display: flex;
        gap: 4rem;
        align-items: center;

        .left {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        .right {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        h5 {
          color: #3D40A8;
        }

        h1 {
          color: black;
        }

        p {
          color: #000;
        }

        .card {
          background-color: #FCFCFC;
          border-radius: 16px;
          padding: 12px 24px;
          border: 1px solid #F7F7F7;

          p {
            color: #000;
          }
        }
      }

      .how-to-apply-steps {
        display: flex;
        flex-direction: column;
        gap: 2rem;

        h3 {
          color: #3D40A8;
          text-align: center;
        }

        .apply-steps {
          display: flex;
          gap: 1rem;



          .step {
            display: flex;
            flex-direction: column;
            gap: 2rem;
            background-color: #FCFCFC;
            border-radius: 24px;
            padding: 12px 24px;
            border: 1px solid #F7F7F7;

            .step-icon {
              display: flex;
              align-items: center;
              justify-content: space-between;

              .step-count {
                display: flex;
                flex-direction: column;
                gap: 0.5rem;

                h1 {
                  color: #2E3192;
                }

                h4 {
                  color: #878787;
                }


              }
            }



          }
        }
      }


    }



  }

  .classes {
    position: relative;
    width: 100%;
    height: 50%;
    object-fit: cover;

    .classes-img {
      img {
        border-radius: 12px;
        width: 100%;
        height: 700px;
        object-fit: cover;
      }


    }

    .classes-card {
      position: absolute;
      top: 1rem;
      right: 1rem;
      gap: 6rem;
      background-color: white;
      padding:2rem;
      border-radius: 12px;
      flex-wrap: wrap;
      z-index: 10;
      display: flex;
      flex-direction: column;
      gap: 1rem;
      max-width: 40%;

      h5 {
        color: #3D40A8;
        font-size: 18px;
      }

      .date-cards {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;

        .date-card {
          display: flex;
          gap: 0.5rem;
          flex: 1;

          .icon {
            background-color: #F9F9F9;
            padding: 16px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #39B54A;
            width: 52px;
            height: 52px;


          }

          .date-text {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;


          }

          p {
            color: #000;
          }
        }
      }

    }

  }

  .tuition-payments {
    display: flex;
    gap: 48px;


    .tuition-payments-img {
      img {
        width: 600px;
        height: 450px;
        object-fit: cover;
        border-radius: 12px;
      }
    }

    .tuition-payments-text {
      display: flex;
      flex-direction: column;
      gap: 24px;
      flex: 1;

      h5 {
        color: #3D40A8;
      }

      h1 {
        color: black;
      }

      p {
        color: #000;
      }

      .card {
        background-color: #F7F8FF;
        border-radius: 16px;
        padding: 12px 24px;

        p {
          color: #000;
        }

        &.deposit-note {
          background-color: #39B54A14;
          border: 1px solid #39B54A14;

          p {
            color: #000;
            font-weight: 600;
            display: flex;
            gap: 10px;
            align-items: center;
          }
        }
      }

      h4 {
        color: #3D40A8;
      }
    }

  }

  .meet-instructor {
    display: grid;
    grid-template-columns: 40% 60%;
    gap: 1rem;
    position: relative;
    border-radius: 32px;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #2E3192;
    overflow: hidden;
    padding: 50px;
    align-items: center;
    justify-content: center;

    .curves-top {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 1;
      width: 50%;
      max-width: 600px;
      height: auto;
      object-fit: contain;
      pointer-events: none;
    }

    .curves-bottom {
      position: absolute;
      bottom: 0;
      left: 0;
      z-index: 1;
      width: 50%;
      max-width: 600px;
      height: auto;
      object-fit: contain;
      pointer-events: none;
    }

    .meet-instructor-img {
      position: relative;
      z-index: 2;

      img {
        object-fit: cover;
        border-radius: 12px;
        width: 100%;
        height: 500px;
      }

    }

    .meet-instructor-text {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      height: 100%;
      background-color: white;
      padding: 3.5rem;
      border-radius: 12px;
      position: relative;
      z-index: 2;
      justify-content: center;


      h5 {
        color: #3D40A8;
        font-size: 18px;
      }

      h1 {
        color: black;
      }

      h4 {
        color: #808080;
      }

      p {
        color: #4D4D4D;
      }

      .contact-cards {
        display: flex;
        gap: 1rem;

        .contact-card {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          background-color: white;
          border-radius: 12px;
          padding: 12px 24px;
          border: 1px solid #F9F9F9;
          flex: 1;

          .card-text {
            display: flex;
            flex-direction: column;
            gap: 1rem;

            h4 {
              color: #828282;
              font-size: 14px;
            }

            p {
              color: black;
              font-size: 16px;
              font-weight: 500;

              a {
                color: black;
                text-decoration: none;
                font-weight: 500;
                font-size: 16px;
              }
            }
          }
        }
      }
    }
  }

  .choose-us {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    align-items: center;

    .choose-us-text {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      align-items: center;
      text-align: center;

      h5 {
        color: #3D40A8;
      }


    }

    .choose-us-content {
      display: flex;
      flex-wrap: wrap;
      gap: 2rem;
      // justify-content: center;


      .card-group {
        display: flex;
        flex-direction: column;
        gap: 1rem;

        .card-text {
          display: flex;
          gap: 0.5rem;
          align-items: center;

          .card {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background-color: #2E3192;
          }

          p {
            max-width: 300px;
            font-size: 16px;
          }
        }
      }


    }
  }
}

@media screen and (max-width: 1440px) {
  .home {
    .hero {
      .left-section {
        .hero-text {
          h1 {
            font-size: 52px;
          }
        }
      }
    }
  }
}

@media screen and (min-width: 1441px) {

  .home {
    .hero {
      .left-section {
        .hero-text {
          h1 {
            font-size: 52px;
          }
        }
      }
    }

    .about-trainings,
    .meet-instructor {

      .curves-top,
      .curves-bottom {
        max-width: 700px;
      }
    }
  }
}

@media screen and (max-width: 1440px) {
  .home {
    .meet-instructor {
      .meet-instructor-text {
        width: 100%;
      }
    }
  }
}



@media screen and (max-width: 834px) {
  .home {
    gap: 60px;
    padding: 16px;
    padding-top: 110px;

    .hero {
      flex-direction: column-reverse;
      gap: 2rem;
      padding: 0 20px;

      .left-section {
        gap: 2rem;
        order: 2;

        .hero-text {
          h1 {
            font-size: 42px;
          }

          h5 {
            font-size: 16px;
          }

          .enrollment {
            flex-direction: row;
            align-items: end;
            gap: 1rem;

            .form-group {
              flex: 1;

              input {
                height: 52px;
                padding: 14px;
              }
            }

            .primary {
              height: 52px;
            }
          }
        }
      }

      .hero-img {
        order: 1;

        img {
          width: 100%;
          height: 400px;
          object-fit: cover;
        }

        .convenient {
          width: 80%;
        }
      }

      .convenient {
        display: flex;
      }
    }

    .about-trainings {
      padding: 0 20px;

      .curves-top,
      .curves-bottom {
        width: 60%;
        max-width: 500px;
      }

      .white-card {
        flex-direction: column-reverse;
        gap: 32px;
        padding: 32px;
        width: calc(100% - 40px);

        .about-trainings-img {
          order: -1;

          img {
            height: 350px;
          }
        }

        .about-trainings-text {

          h1 {
            font-size: 28px;
          }
        }
      }

      .class-section {
        padding: 0 30px 60px;

        .section-header {
          h2 {
            font-size: 28px;
          }
        }

        .class-cards {
          width: 95%;

          .card-row {
            grid-template-columns: 1fr 1fr;
            gap: 12px;
          }

          .class-card {
            padding: 20px;

            &.full-width {
              grid-column: span 2;
            }

            .card-content {
              h3 {
                font-size: 15px;
              }
            }
          }
        }
      }
    }

    .How-to-apply {
      gap: 1.5rem;

      .apply-text {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;

        .left,
        .right {
          flex: none;
        }

        h1 {
          font-size: 28px;
        }
      }

      .how-to-apply-steps {
        .apply-steps {
          flex-wrap: wrap;
          justify-content: center;
          gap: 1rem;

          .step {
            flex: 0 1 calc(50% - 0.5rem);
            min-width: 300px;
            flex-direction: row;
            align-items: center;
            text-align: left;

            .step-count {
              min-width: 80px;
              padding: 0.75rem;

              h1 {
                font-size: 40px;
              }

              svg {
                width: 28px;
                height: 28px;
              }
            }

            .step-text {
              flex: 1;
            }
          }
        }
      }
    }

    .classes {
      .classes-img {
        img {
          height: 500px;
        }
      }

      .classes-card {
        max-width: 70%;
        padding: 1.5rem;

        h1 {
          font-size: 24px;
        }

        .date-cards {
          flex-direction: row;
          gap: 1rem;

          .date-card {
            flex: 1;
          }
        }
      }
    }

    .tuition-payments {
      flex-direction: column;
      gap: 2rem;
      text-align: center;

      .tuition-payments-img {
        img {
          width: 100%;
          height: 350px;
          object-fit: cover;
        }
      }

      .tuition-payments-text {
        max-width: 80%;
        align-items: center;

        h1 {
          font-size: 28px;
        }
      }
    }

    .meet-instructor {
      flex-direction: column;
      gap: 1.5rem;
      padding: 30px;

      .curves-top,
      .curves-bottom {
        width: 60%;
        max-width: 500px;
      }

      .meet-instructor-img {
        img {
          width: 100%;
          height: 350px;
          object-fit: cover;
        }
      }

      .meet-instructor-text {
        width: 100%;
        padding: 2.5rem;
        text-align: center;

        h1 {
          font-size: 28px;
        }

        .contact-cards {
          flex-direction: row;
          justify-content: center;
          gap: 1rem;

          .contact-card {
            flex: 1;
            max-width: 200px;
          }
        }
      }
    }

    .choose-us {
      .choose-us-text {
        h2 {
          font-size: 24px;
          max-width: 90%;
        }
      }

      .choose-us-content {
        max-width: 80%;

        .card-group {
          flex-direction: row;
          justify-content: center;
          gap: 2rem;

          .card-text {
            flex: 1;
            max-width: 250px;
          }
        }
      }
    }
  }
}


@media screen and (max-width: 1024px) {
  .home {
    gap: 80px;
    padding-top: 120px;

    .hero {
      grid-template-columns: 45% 50%;
      gap: 3rem;
      padding: 0 30px;

      .left-section {
        gap: 3rem;

        .hero-text {
          h1 {
            font-size: 48px;
          }

          .enrollment {
            .form-group {
              input {
                height: 54px;
                padding: 15px;
              }
            }

            .primary {
              height: 54px;
            }
          }
        }
      }

      .hero-img {
        img {
          height: 500px;
        }
      }
    }

    .about-trainings {

      .curves-top,
      .curves-bottom {
        width: 60%;
        max-width: 500px;
      }

      .white-card {
        grid-template-columns: 1fr 1fr;
        gap: 40px;
        padding: 40px;

        .about-trainings-text {
          h1 {
            font-size: 30px;
          }
        }

        .about-trainings-img {
          img {
            height: 450px;
          }
        }
      }

      .class-section {
        padding: 0 40px 70px;

        .class-cards {
          width: 90%;

          .card-row {
            grid-template-columns: 1fr 1fr;
          }

          .class-card {
            padding: 22px;

            &.full-width {
              grid-column: span 2;
            }
          }
        }
      }
    }

    .How-to-apply {
      .apply-text {
        flex-direction: row;
        gap: 3rem;

        h1 {
          font-size: 30px;
        }
      }

      .how-to-apply-steps {
        .apply-steps {
          flex-wrap: wrap;
          justify-content: center;

          .step {
            flex: 0 1 calc(50% - 0.5rem);
            min-width: 350px;

            .step-count {
              min-width: 90px;

              h1 {
                font-size: 44px;
              }

              svg {
                width: 30px;
                height: 30px;
              }
            }
          }
        }
      }
    }

    .classes {
      .classes-img {
        img {
          height: 600px;
        }
      }

      .classes-card {
        max-width: 60%;

        h1 {
          font-size: 28px;
        }
      }
    }

    .tuition-payments {
      flex-direction: row;
      gap: 3rem;

      .tuition-payments-img {
        flex: 1;

        img {
          height: 400px;
        }
      }

      .tuition-payments-text {
        flex: 1;
        max-width: none;

        h1 {
          font-size: 30px;
        }
      }
    }

    .meet-instructor {
      flex-direction: row;
      gap: 2rem;
      padding: 40px;

      .curves-top,
      .curves-bottom {
        width: 60%;
        max-width: 500px;
      }

      .meet-instructor-img {
        flex: 1;

        img {
          height: 400px;
        }
      }

      .meet-instructor-text {
        flex: 1;
        padding: 3rem;

        h1 {
          font-size: 30px;
        }

        .contact-cards {
          flex-direction: column;
          gap: 1rem;
        }
      }
    }

    .choose-us {
      .choose-us-text {
        h2 {
          font-size: 28px;
        }
      }

      .choose-us-content {
        .card-group {
          flex-direction: row;
          justify-content: space-between;
          gap: 1.5rem;

          .card-text {
            flex: 1;
          }
        }
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .home {
    padding: 0;
    padding-top: 100px;

    .hero {
      grid-template-columns: 1fr;
      gap: 1rem;
      padding: 0 10px;

      .left-section {
        gap: 4rem;

        .hero-text {
          h1 {
            font-size: 32px;
          }

          h5 {
            font-size: 14px;
          }

          .enrollment {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;

            .form-group {
              flex: none;

              input {
                height: 48px;
                padding: 12px;
              }
            }

            .primary {
              width: 100%;
              justify-content: center;
              height: 48px;
            }
          }
        }
      }

      .convenient {
        display: none;
      }

      .hero-img {
        img {
          width: 95%;
          height: auto;
        }

        .convenient {
          width: 100%;
        }
      }
    }


    .about-trainings {
      padding: 0 14px;
      border-radius: 0;

      .white-card {
        grid-template-columns: 1fr;
        gap: 32px;
        padding: 24px;
        width: calc(100% - 28px);

        .about-trainings-img {
          order: -1;

          img {
            height: auto;
          }
        }
      }

      .class-section {
        padding: 0 20px 40px;

        .class-cards {
          width: 100%;

          .card-row {
            grid-template-columns: 1fr;
          }

          .class-card {
            &.full-width {
              grid-column: span 1;
            }
          }
        }
      }

      .curves-top {
        width: 70%;
        max-width: none;
      }

      .curves-bottom {
        width: 70%;
        max-width: none;
      }
    }

    .How-to-apply {
      .apply-text {
        flex-direction: column;
        gap: 1rem;

        .left,
        .right {
          flex: none;
        }
      }

      .how-to-apply-steps {
        .apply-steps {
          flex-direction: column;
          gap: 1rem;
          align-items: center;
          text-align: center;

          .step {
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            text-align: center;

            .step-count {
              min-width: auto;
              padding: 0.5rem;

              h1 {
                font-size: 36px;
              }

              svg {
                width: 24px;
                height: 24px;
              }
            }
          }
        }

      }
    }

    .classes {
      .classes-img {
        img {
          width: 100%;
          height: auto;
          border-radius: 0;
        }
      }

      .classes-card {
        max-width: 90%;

        .date-cards {
          flex-direction: column;
        }
      }
    }

    .tuition-payments {
      flex-direction: column;
      gap: 1rem;
      align-items: center;
      text-align: center;
      justify-content: center;

      .tuition-payments-img {
        img {
          width: 100%;
          height: auto;
        }
      }

      .tuition-payments-text {
        max-width: 90%;
        align-items: center;
      }
    }

    .meet-instructor {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      padding: 20px;
      background-size: cover;
      border-radius: 0;

      .curves-top {
        width: 70%;
        max-width: none;
      }

      .curves-bottom {
        width: 70%;
        max-width: none;
      }



      .meet-instructor-img {

        img {
          width: 100%;
          height: 100%;

        }
      }

      .meet-instructor-text {
        width: 100%;
        padding: 2rem;

        .contact-cards {
          flex-direction: column;
        }
      }
    }

    .choose-us {
      .choose-us-text {
        h2 {
          max-width: 90%;
        }
      }

      .choose-us-content {
        flex-direction: column;
        align-items: center;

        .card-group {
          flex-direction: column;
          align-items: center;
        }
      }
    }

    .contact-container {
      .contact-wrapper {
        grid-template-columns: 1fr;
      }

      .contact-left {
        gap: 1rem;
      }

      .contact-right {
        form {
          gap: 1rem;
        }
      }

      .contact-cards {
        flex-direction: column;
        gap: 1rem;
      }

      .contact-card {
        flex-direction: column;
        gap: 1rem;
      }

      .card-text {
        flex-direction: column;
        gap: 1rem;
      }

      .contact-header {
        gap: 1rem;
      }
    }

  }

}