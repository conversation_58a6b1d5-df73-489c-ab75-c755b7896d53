.course {
    display: flex;
    flex-direction: column;
    gap: 100px;
    padding: 24px;

    .hero {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        align-items: center;

        .hero-text {
            display: flex;
            flex-direction: column;
            gap: 1rem;

            h5 {
                color: #3D40A8;
                font-size: 18px;
            }

            h1 {
                color: black;
            }

            p {
                color: #000;
            }
        }

        .hero-img {
            img {
                width: 700px;
                height: 400px;
                object-fit: cover;
                border-radius: 12px;
            }
        }
    }

    .hands-on {
        width: 100%;
        height: 912px;
        position: relative;
        padding: 24px 100px;
        border-radius: 32px;
        background-color: #2E3192;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        gap: 24px;

        .curves-top {
            position: absolute;
            top: 660px;
            right: -80px;
            z-index: 1;
            width: 50%;
            max-width: 600px;
            height: auto;
            object-fit: contain;
            pointer-events: none;
        }

        .curves-bottom {
            position: absolute;
            top: -2px;
            left: -54.5px;
            z-index: 1;
            width: 50%;
            max-width: 600px;
            height: auto;
            object-fit: contain;
            pointer-events: none;
        }

        .hands-on-text {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 2;

            h5 {
                color: #39B54A;
                font-size: 18px;
            }

            h1 {
                color: white;
            }
        }

        .hands-on-img {
            position: relative;
            width: 100%;
            height: 720px;
            object-fit: cover;
            border-radius: 12px;
            z-index: 2;



            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 32px;
            }

            .texts {


                .text-top-left {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 25%;
                    padding: 2rem;
                    margin: 24px;
                    border-radius: 12px;
                    background-color: #FFFFFFCC;
                    font-weight: 700;
                }

                .text-bottom-right {
                    position: absolute;
                    bottom: 0;
                    right: 0;
                    width: 25%;
                    padding: 2rem;
                    margin: 24px;
                    border-radius: 12px;
                    background-color: #FFFFFFCC;
                    font-weight: 700;
                }
            }




        }
    }

    .what-you-learn {
        display: flex;
        flex-direction: column;
        gap: 1rem;

        .what-you-learn-text {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            align-items: center;
            justify-content: center;

            h5 {
                color: #2E3192;
                font-size: 18px;
            }

            h1 {
                color: black;
            }

            p {
                color: #2E3192;
            }
        }

        .cards {
            display: flex;
            flex-wrap: wrap;
            gap: 2rem;

            .card {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                gap: 1rem;
                background-color: #FBFBFB;
                border-radius: 16px;
                padding: 16px 24px;
                width: 250px;

                p {
                    color: #000;
                }
            }
        }

    }

    .FAQ {
        display: flex;
        gap: 1rem;

        .FAQ-text {
            display: flex;
            flex-direction: column;
            gap: 1rem;


            h5 {
                color: #2E3192;
                font-size: 18px;
            }
        }

        .FAQ-cards {
            display: flex;
            flex-direction: column;
            gap: 1rem;

            .FAQ-card {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                justify-content: center;
                gap: 1rem;
                background-color: #FBFBFB;
                border-radius: 16px;
                padding: 16px 24px;


            }
        }
    }

}


@media screen and (min-width: 1441px) {
    .course {
        .hands-on {

            .curves-top,
            .curves-bottom {
                max-width: 700px;
            }
        }
    }
}

// Tablet Portrait (769px - 834px)
@media screen and (min-width: 769px) and (max-width: 834px) {
    .course {
        .hero {
            grid-template-columns: 1fr;
            gap: 2rem;
            text-align: center;

            .hero-text {
                h1 {
                    font-size: 28px;
                }

                h5 {
                    font-size: 16px;
                }

                p {
                    font-size: 15px;
                }
            }

            .hero-img {
                img {
                    height: 350px;
                    object-fit: cover;
                }
            }
        }

        .hands-on {
            padding: 20px 40px;

            .curves-top,
            .curves-bottom {
                width: 60%;
                max-width: 500px;
            }

            .hands-on-text {
                text-align: center;

                h1 {
                    font-size: 24px;
                }

                h5 {
                    font-size: 16px;
                }
            }

            .hands-on-img {
                height: 300px;

                .texts {
                    .text-top-left,
                    .text-bottom-right {
                        padding: 12px 16px;

                        p {
                            font-size: 13px;
                        }
                    }
                }
            }
        }

        .what-you-learn {
            .what-you-learn-text {
                text-align: center;

                h1 {
                    font-size: 28px;
                }
            }

            .cards {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;

                .card {
                    padding: 16px 20px;

                    p {
                        font-size: 14px;
                    }
                }
            }
        }

        .FAQ {
            .FAQ-text {
                text-align: center;

                h3 {
                    font-size: 24px;
                }
            }

            .FAQ-cards {
                grid-template-columns: 1fr;
                gap: 1rem;

                .FAQ-card {
                    padding: 20px;

                    h4 {
                        font-size: 16px;
                    }

                    p {
                        font-size: 14px;
                    }
                }
            }
        }
    }
}

// Tablet Landscape (835px - 1024px)
@media screen and (min-width: 835px) and (max-width: 1024px) {
    .course {
        .hero {
            grid-template-columns: 1fr 1fr;
            gap: 3rem;

            .hero-text {
                h1 {
                    font-size: 32px;
                }

                h5 {
                    font-size: 17px;
                }
            }

            .hero-img {
                img {
                    height: 400px;
                }
            }
        }

        .hands-on {
            padding: 24px 60px;

            .curves-top,
            .curves-bottom {
                width: 60%;
                max-width: 500px;
            }

            .hands-on-text {
                h1 {
                    font-size: 28px;
                }
            }

            .hands-on-img {
                height: 350px;

                .texts {
                    .text-top-left,
                    .text-bottom-right {
                        padding: 14px 18px;

                        p {
                            font-size: 14px;
                        }
                    }
                }
            }
        }

        .what-you-learn {
            .what-you-learn-text {
                h1 {
                    font-size: 32px;
                }
            }

            .cards {
                grid-template-columns: repeat(3, 1fr);
                gap: 1.5rem;

                .card {
                    padding: 18px 22px;
                }
            }
        }

        .FAQ {
            .FAQ-text {
                h3 {
                    font-size: 28px;
                }
            }

            .FAQ-cards {
                grid-template-columns: repeat(2, 1fr);
                gap: 1.5rem;

                .FAQ-card {
                    padding: 22px;

                    h4 {
                        font-size: 17px;
                    }
                }
            }
        }
    }
}

@media screen and (max-width: 768px) {
    .course {
        padding: 0;

        .hero {
            grid-template-columns: 1fr;
            gap: 1rem;

            .hero-text {
                gap: 0.5rem;
            }

            .hero-img {
                img {
                    width: 100%;
                    height: auto;
                }
            }
        }

        .hands-on {
            padding: 24px;
            height: 60vh;
            border-radius: 0;

            .curves-top {
                width: 70%;
                max-width: none;
            }

            .curves-bottom {
                width: 70%;
                max-width: none;
            }

            .hands-on-text {
                gap: 0.5rem;
            }

            .hands-on-img {
                height: 300px;

                img {
                    height: 40vh;
                }

                .texts {
                    display: flex;
                    flex-direction: column;

                    .text-top-left {
                        width: 95%;
                        bottom: 0;
                        right: 0;
                        height: 110px;
                        margin: 10px;
                    }

                    .text-bottom-right {
                        width: 95%;
                        left: 0;
                        height: 110px;
                        margin: 10px;
                        top: 375px;
                    }
                }


            }
        }

        .what-you-learn {
            .what-you-learn-text {
                gap: 0.5rem;
            }

            .cards {
                flex-direction: column;
                gap: 1rem;

                .card {
                    width: 100%;
                }
            }
        }

        .FAQ {
            flex-direction: column;
            gap: 1rem;

            .FAQ-text {
                gap: 0.5rem;
            }

            .FAQ-cards {
                gap: 1rem;
            }
        }
    }

}