.navigation{
    display: flex;
    justify-content: space-between;
    // align-items: center;
    position: relative;



    .logo{
        img{
            width: 66px;
            height: 48px;
            object-fit: cover;
        }
    }

    .menu-toggle {
        display: none;
        cursor: pointer;
        z-index: 100;
    }

    .nav-links{
        align-items: center;
        gap: 3rem;
        display: flex;
        justify-content: center;
        font-weight: bold;
    }


}

// Tablet Portrait (769px - 834px)
@media (min-width: 769px) and (max-width: 834px) {
    .navigation {
        padding: 1rem 2rem;

        .logo {
            img {
                width: 60px;
                height: 44px;
            }
        }

        .nav-links {
            gap: 1.5rem;

            a {
                font-size: 15px;
            }

            .contact-us {
                button {
                    padding: 10px 20px;
                    font-size: 14px;
                }
            }
        }

        .menu-toggle {
            display: none;
        }
    }
}

// Tablet Landscape (835px - 1024px)
@media (min-width: 835px) and (max-width: 1024px) {
    .navigation {
        padding: 1rem 3rem;

        .logo {
            img {
                width: 64px;
                height: 46px;
            }
        }

        .nav-links {
            gap: 2rem;

            a {
                font-size: 16px;
            }

            .contact-us {
                button {
                    padding: 12px 24px;
                    font-size: 15px;
                }
            }
        }

        .menu-toggle {
            display: none;
        }
    }
}

@media (max-width: 768px) {

    .navigation{
        .logo {
            z-index: 101;
            position: relative;
        }

        .menu-toggle {
            display: block;
        }

        .nav-links {
            position: fixed;
            top: 0;
            right: -100%;
            width: 100%;
            height: 100%;
            background-color: white;
            flex-direction: column;
            padding: 2rem;
            transition: right 0.3s ease;
            z-index: 99;

            &.active {
                right: 0;
            }

            a {
                padding: 1rem 0;
                font-size: 1.2rem;
            }

            .contact-us {
                width: 100%;

                button {
                    width: 100%;
                }
            }
        }
    }

}