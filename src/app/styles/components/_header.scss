.navigation{
    display: flex;
    justify-content: space-between;
    // align-items: center;
    position: relative;



    .logo{
        img{
            width: 66px;
            height: 48px;
            object-fit: cover;
        }
    }

    .menu-toggle {
        display: none;
        cursor: pointer;
        z-index: 100;
    }

    .nav-links{
        align-items: center;
        gap: 3rem;
        display: flex;
        justify-content: center;
        font-weight: bold;
    }


}

@media (max-width: 768px) {

    .navigation{
        .logo {
            z-index: 101; 
            position: relative;
        }

        .menu-toggle {
            display: block;
        }

        .nav-links {
            position: fixed;
            top: 0;
            right: -100%;
            width: 100%;
            height: 100%;
            background-color: white;
            flex-direction: column;
            padding: 2rem;
            transition: right 0.3s ease;
            z-index: 99;

            &.active {
                right: 0;
            }

            a {
                padding: 1rem 0;
                font-size: 1.2rem;
            }

            .contact-us {
                width: 100%;

                button {
                    width: 100%;
                }
            }
        }
    }

}