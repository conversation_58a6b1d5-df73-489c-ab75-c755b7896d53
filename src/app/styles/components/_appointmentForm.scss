
.appointment-form {
  background-color: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 700px;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 24px;
  
  
  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;

    h1{
      font-family: 18px;
    }
    
   
  }
  
  .progress-bar {
    height: 8px;
    background-color: #F0F0F0;
    border-radius: 4px;
    overflow: hidden;
    
    .progress {
      height: 100%;
      background-color: #39B54A;
      transition: width 0.3s ease;
    }
  }
  
  form {
    display: flex;
    flex-direction: column;
    gap: 24px;

    .half {
      display: flex;
      gap: 16px;
      
      .form-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
        width: 100%;
        
        label {
          font-weight: 500;
          color: #333;
        }
        
        input, select {
          width: 100%;
          padding: 12px;
          border: 1px solid #E0E0E0;
          border-radius: 8px;
          font-size: 14px;
          
       
        }
      }
    }
    
    .checkbox-option {
      display: flex;
      align-items: center;
      gap: 8px;
      
      input[type="checkbox"] {
        width: 18px;
        height: 18px;
      }
      
      label {
        font-size: 14px;
        color: #555;
      }
    }
    
    .payments {
     display: grid;
 grid-template-columns: 40% 60%;
     gap: 1rem;
      
      .fees {
        background-color: #FCFCFC;
        padding: 20px;
        border-radius: 12px;
        display: flex;
        flex-direction: column;
      
        gap: 1rem;
        
        h3 {
          color: #555;
          font-size: 16px;
        }
        
        h1 {
          display: flex;
          align-items: center;
          gap: 2rem;
          
          span {
            font-size: 16px;
            color: #666;
          }
        }
      }
      
      .options {
        display: flex;
        flex-direction: column;
        gap: 16px;
        width: 100%;

        
        h1 {
          font-size: 18px;
          color: #333;
        }
        
        .card {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px;
          border: 1px solid #E0E0E0;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.2s ease;
          width: 100%;
          
          
          p {
            font-weight: 500;
          }
          
          img {
            width: 20px;
            height: 20px;
            object-fit: cover;

          }
          .icon{
            width: 40px;
            height: 40px;
            padding: 10px;
            background-color: #F4F9FF;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
    
    .actions {
      display: flex;
      width: 100%;
      gap: 1rem;
     
      
      button {
        width:  100%;
      }
    }
  }
}

.file-upload-container {
  cursor: pointer;
  width: 100%;
  
  .file-input-hidden {
    display: none;
  }
  
  .file-upload-box {
    border: 1px solid #E0E0E0;
    border-radius: 8px;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 80px;
    transition: all 0.2s ease;
    
   
    
    .file-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      text-align: center;
      
      span {
        font-size: 14px;
        color: #666;
      }
    }
    
    .file-selected {
      display: flex;
      align-items: center;
      gap: 8px;
      
      span {
        font-size: 14px;
        color: #333;
        font-weight: 500;
      }
    }
  }
}

@media (max-width: 768px) {
  .appointment-form {
    padding: 24px 16px;
    
    .title h1 {
      font-size: 20px;
    }
    
    form {
      gap: 16px;
      
      .half {
        flex-direction: column;
        gap: 16px;
      }
      
      .actions {
        flex-direction: column-reverse;
        gap: 12px;
        
        button {
          width: 100%;
        }
      }
    }
  }
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  overflow-y: auto;
}

.file-upload-container {
  cursor: pointer;
  width: 100%;
}

.file-input-hidden {
  display: none;
}

.file-upload-box {
  border: 1px solid #F5F5F5;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #FBFBFB;

  
  .file-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    color: #666;
    text-align: center;
    
  }
  
  .file-selected {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #333;
    font-weight: 500;

  }
}

