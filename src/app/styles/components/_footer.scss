.footer {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding-top:300px;


    .quick-links {
        display: flex;
        justify-content: space-between;

        a{
            color: #000;
            font-weight: 500;
            width: fit-content;

        }
        ul{
            font-weight: 400;
        }

        .logo-text {
            display: flex;
            flex-direction: column;
            gap: 1rem;

            img {
                width: 66px;
                height: 48px;
                object-fit: cover;
            }
        }

        .footer-links {
            display: flex;
            flex-direction: column;
            gap: 24px;

            h3 {
                color: #098241;
            }

        }

        .contacts {

            ul {
                display: flex;
                flex-direction: column;
                gap: 24px;

                li {
                    display: flex;
                    gap: 1rem;
                    align-items: center;
                }
            }
        }

        .updates {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            background-color: #F8F8F8;
            padding: 24px 32px;
            border-radius: 1rem;

            .date {
                color: #2E3192;

            }
        }


    }

    .footer-copyright {
        display: flex;
        justify-content: space-between;

        .copyright{
            display: flex;
            gap: 5px;
            align-items: center;

            p{
                color: #5F5F5F;
            }
        }

        .policies {
            display: flex;
            gap: 1rem;

            p{
                color: #5F5F5F;
            }
        }
    }

}

@media screen and (max-width: 1440px) {
    .footer{
        padding: 1rem 2rem;
        padding-top: 300px;
    }
    
}
@media screen and (min-width: 769px) and (max-width: 834px) {
    .footer {
        padding: 3rem 2rem 2rem;

        .footer-content {
            flex-direction: column;
            gap: 2rem;
            text-align: center;

            .footer-left {
                .footer-logo {
                    img {
                        width: 60px;
                        height: 44px;
                    }
                }

                p {
                    font-size: 15px;
                    max-width: 80%;
                    margin: 0 auto;
                }
            }

            .footer-right {
                .footer-links {
                    flex-direction: row;
                    justify-content: center;
                    gap: 2rem;

                    .footer-column {
                        text-align: center;

                        h4 {
                            font-size: 16px;
                        }

                        ul li a {
                            font-size: 14px;
                        }
                    }
                }
            }
        }

        .footer-copyright {
            flex-direction: column;
            gap: 1rem;
            text-align: center;

            .copyright {
                justify-content: center;

                p {
                    font-size: 14px;
                }
            }

            .policies {
                justify-content: center;
                gap: 1.5rem;

                p {
                    font-size: 14px;
                }
            }
        }
    }

    .refund-popup, .accreditation-popup {
        width: 85%;
        max-width: 600px;
        padding: 2rem;

        .contents {
            padding: 1.5rem;
        }
    }
}

// Tablet Landscape (835px - 1024px)
@media screen and (min-width: 835px) and (max-width: 1024px) {
    .footer {
        padding: 4rem 3rem 2rem;

        .footer-content {
            flex-direction: row;
            gap: 3rem;

            .footer-left {
                flex: 1;

                .footer-logo {
                    img {
                        width: 64px;
                        height: 46px;
                    }
                }

                p {
                    font-size: 16px;
                    max-width: 90%;
                }
            }

            .footer-right {
                flex: 1;

                .footer-links {
                    flex-direction: row;
                    gap: 2rem;

                    .footer-column {
                        flex: 1;

                        h4 {
                            font-size: 17px;
                        }

                        ul li a {
                            font-size: 15px;
                        }
                    }
                }
            }
        }

        .footer-copyright {
            flex-direction: row;
            justify-content: space-between;

            .copyright {
                p {
                    font-size: 15px;
                }
            }

            .policies {
                gap: 2rem;

                p {
                    font-size: 15px;
                }
            }
        }
    }

    .refund-popup, .accreditation-popup {
        width: 80%;
        max-width: 650px;
        padding: 2.5rem;

        .contents {
            padding: 2rem;
        }
    }
}

@media screen and (max-width: 768px) {
    .refund-popup, .accreditation-popup {
        padding: 1.5rem;

        .contents {
            padding: 1rem;
        }
    }

    .footer {
        text-align: center;

        .quick-links {
            flex-direction: column;
            gap: 2rem;

            .logo-text {
                gap: 1rem;
                align-items: center;
            }

            .footer-links {
                gap: 1rem;
                align-items: center;
            }

            .contacts {
                ul {
                    gap: 1rem;
                    display: flex;
                    align-items: center;
                }
            }

            .updates {
                gap: 1rem;
            }
        }

        .footer-copyright {
            flex-direction: column-reverse;
            gap: 1rem;

            .policies {
                flex-direction: column;
            }
        }
    }

}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.refund-popup, .accreditation-popup {
  width: 90%;
  max-width: 500px;
  background-color: white;
  border-radius: 16px;
  padding: 2.5rem;
  position: relative;
  z-index: 1001;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;

  .contents{
    background-color:#F8F8F8;
    border-radius: 12px;
    padding: 24px;
    display :flex;
    flex-direction: column;
    gap: 1rem;
    justify-content: center;
    align-items: center;
  }

  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    h3 {
      color: black;
    }
  }

  .icon {
    img {
      width: 100px;
      height: 100px;
      object-fit: cover;
    }
  }

  h4 {
    color: #2E3192;
  }

  p {
    color: #000;
    text-align: center;
  }
}
