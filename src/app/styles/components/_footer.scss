.footer {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding-top:100px;

    .quick-links {
        display: flex;
        justify-content: space-between;

        a{
            color: #000;
            font-weight: 500;

        }
        ul{
            font-weight: 400;
        }

        .logo-text {
            display: flex;
            flex-direction: column;
            gap: 1rem;

            img {
                width: 66px;
                height: 48px;
                object-fit: cover;
            }
        }

        .footer-links {
            display: flex;
            flex-direction: column;
            gap: 24px;

            h3 {
                color: #098241;
            }

        }

        .contacts {

            ul {
                display: flex;
                flex-direction: column;
                gap: 24px;

                li {
                    display: flex;
                    gap: 1rem;
                    align-items: center;
                }
            }
        }

        .updates {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            background-color: #F8F8F8;
            padding: 24px 32px;
            border-radius: 1rem;

            .date {
                color: #2E3192;

            }
        }


    }

    .footer-copyright {
        display: flex;
        justify-content: space-between;

        .copyright{
            display: flex;
            gap: 5px;
            align-items: center;

            p{
                color: #5F5F5F;
            }
        }

        .policies {
            display: flex;
            gap: 1rem;

            p{
                color: #5F5F5F;
            }
        }
    }

}

@media screen and (max-width: 768px) {
    .refund-popup, .accreditation-popup {
        padding: 1.5rem;

        .contents {
            padding: 1rem;
        }
    }

    .footer {
        text-align: center;

        .quick-links {
            flex-direction: column;
            gap: 2rem;

            .logo-text {
                gap: 1rem;
                align-items: center;
            }

            .footer-links {
                gap: 1rem;
                align-items: center;
            }

            .contacts {
                ul {
                    gap: 1rem;
                    display: flex;
                    align-items: center;
                }
            }

            .updates {
                gap: 1rem;
            }
        }

        .footer-copyright {
            flex-direction: column-reverse;
            gap: 1rem;

            .policies {
                flex-direction: column;
            }
        }
    }

}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.refund-popup, .accreditation-popup {
  width: 90%;
  max-width: 500px;
  background-color: white;
  border-radius: 16px;
  padding: 2.5rem;
  position: relative;
  z-index: 1001;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;

  .contents{
    background-color:#F8F8F8;
    border-radius: 12px;
    padding: 24px;
    display :flex;
    flex-direction: column;
    gap: 1rem;
    justify-content: center;
    align-items: center;
  }

  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    h3 {
      color: black;
    }
  }

  .icon {
    img {
      width: 100px;
      height: 100px;
      object-fit: cover;
    }
  }

  h4 {
    color: #2E3192;
  }

  p {
    color: #000;
    text-align: center;
  }
}
