.contact-container {
    width: 100%;
    position: relative;
    padding: 76px 165px;
    border-radius: 32px;
    top: 0;
    left: 0;
    width: 100%;
    background-color: #2E3192;
    border: 1px solid #F4F4F4;

    .curves-top {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 1;
      width: 50%;
      max-width: 600px;
      height: auto;
      object-fit: contain;
      pointer-events: none;
    }

    .curves-bottom {
      position: absolute;
      bottom: 0;
      left: 0;
      z-index: 1;
      width: 50%;
      max-width: 600px;
      height: auto;
      object-fit: contain;
      pointer-events: none;
    }


    .contact-wrapper{
        background-color: white;
        border: 1px solid #F4F4F4;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        padding: 2.5rem;
        border-radius: 16px;
        position: relative;
        z-index: 2;
        top: 150px;
        width: 100%;

        .contact-left{
            display: flex;
            flex-direction: column;
            gap: 1rem;

            .contact-header{
              display: flex;
              flex-direction: column;
              gap:1rem;
              h5{
                color: #2E3192;
              }

            }

            .contact-cards{
                display: flex;
                gap: 1rem;

                .contact-card {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    background-color: #FBFBFB;
                    border-radius: 12px;
                    padding: 12px 24px;

                    .card-text {
                        display: flex;
                        flex-direction: column;
                        gap:1rem;
                        h4 {
                            color: #2E3192;
                            font-size: 14px;
                        }

                        p {
                            color: #39B54A;
                            font-size: 14px;
                        }
                    }
                }
            }

        }

        .contact-right {
            form {
                display: flex;
                flex-direction: column;
                gap: 1.5rem;

                input, textarea {
                    padding: 16px;
                    border-radius: 12px;
                    border: 1px solid #E0E0E0;
                    font-size: 16px;
                    min-height: 48px;
                }

                textarea {
                    min-height: 120px;
                    resize: vertical;
                }

                .primary{
                    width: 100%;
                    min-height: 56px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

            }


        }

    }


}



@media (min-width: 1441px) {
    .contact-container {
        .curves-top, .curves-bottom {
            max-width: 700px;
        }
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .contact-container {
        .curves-top, .curves-bottom {
            width: 60%;
            max-width: 500px;
        }
    }
}

@media (max-width: 768px) {

    .contact-container{
        padding: 12px;
        border-radius: 0;

        .curves-top {
            width: 70%;
            max-width: none;
        }

        .curves-bottom {
            width: 70%;
            max-width: none;
        }
        .contact-wrapper {
            grid-template-columns: 1fr;
            padding: 12px;
            .contact-left {
                gap: 1rem;

                .contact-header{
                    width: 55%;
                    h1{
                        font-size: 24px;


                    }
                }
                .contact-cards{
                    flex-direction: column;
                    width: 55%;
                }

            }
            .contact-right{

                width: 55%;
                form{
                    gap: 1rem;

                }
            }


        }
    }

}