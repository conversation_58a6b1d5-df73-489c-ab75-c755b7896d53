.contact-container {
    width: 100%;
    position: relative;
        padding: 0 92px;
    border-radius: 32px;
    top: 0;
    left: 0;
    width: 100%;
    background-color: #2E3192;
    border: 1px solid #F4F4F4;

    .curves-top {
      position: absolute;
         top: 53px;
    right: 540px;
      z-index: 1;
      width: 60%;
      max-width: 700px;
      height: auto;
      object-fit: contain;
      pointer-events: none;
      opacity: 0.6;
      transform: scale(1.2);
    }

    .curves-bottom {
      position: absolute;
      bottom: -20px;
      left: -50px;
      z-index: 1;
      width: 60%;
      max-width: 700px;
      height: auto;
      object-fit: contain;
      pointer-events: none;
      opacity: 0.6;
      transform: scale(1.2);
    }


    .contact-wrapper{
        background-color: white;
        border: 1px solid #F4F4F4;
        display: grid;
        grid-template-columns: 50% 45%;
        gap: 2rem;
        padding: 2.5rem;
        border-radius: 16px;
        position: relative;
        z-index: 5;
        top: 250px;

        .contact-left{
            display: flex;
            flex-direction: column;
            gap: 1rem;

            .contact-header{
              display: flex;
              flex-direction: column;
              gap:1rem;
              h5{
                color: #2E3192;
                font-size: 16px;
              }

            }

            .contact-cards{
                display: flex;
                gap: 1rem;
                flex-direction: column;

                .group-card{
                    display: flex;
                   justify-content: space-between;
                }

                .contact-card {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    background-color: #FBFBFB;
                    border-radius: 12px;
                    padding: 12px 20px;

                    .card-text {
                        display: flex;
                        flex-direction: column;
                        gap:1rem;
                        h4 {
                            color: #2E3192;
                            font-size: 16px;
                            font-weight: 400;
                        }

                        p {
                            color: black;
                            font-size: 18px;
                            font-weight: 500;
                        }
                    }
                }
            }

        }

        .contact-right {
            form {
                display: flex;
                flex-direction: column;
                gap: 1.5rem;

                .primary{
                    width: 100%;
                    min-height: 56px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

            }


        }

    }


}



@media (min-width: 1441px) {
    .contact-container {
        .curves-top, .curves-bottom {
            max-width: 800px;
            opacity: 0.6;
            z-index: 1;
            transform: scale(1.3);
        }
    }
}


@media (min-width: 769px) and (max-width: 834px) {
    .contact-container {
        padding: 50px 40px;

        .curves-top, .curves-bottom {
            width: 70%;
            max-width: 600px;
            opacity: 0.5;
            z-index: 1;
            transform: scale(1.1);
        }

        .contact-wrapper {
            grid-template-columns: 1fr;
            gap: 2rem;
        }

        .contact-header {
            text-align: center;

            h1 {
                font-size: 28px;
            }
        }

        .contact-left {
            .contact-cards {
                flex-direction: row;
                justify-content: center;
                gap: 1rem;

                .contact-card {
                    flex: 1;
                    max-width: 200px;
                }
            }
        }

        .contact-right {
            form {
                gap: 1.5rem;

                input, textarea {
                    padding: 14px;
                    min-height: 50px;
                    font-size: 15px;
                }

                textarea {
                    min-height: 100px;
                }

                .primary {
                    min-height: 50px;
                }
            }
        }
    }
}


@media (min-width: 835px) and (max-width: 1024px) {
    .contact-container {
        padding: 60px 80px;

        .curves-top, .curves-bottom {
            width: 70%;
            max-width: 650px;
            opacity: 0.5;
            z-index: 1;
            transform: scale(1.15);
        }

        .contact-wrapper {
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
        }

        .contact-header {
            h1 {
                font-size: 30px;
            }
        }

        .contact-left {
            .contact-cards {
                flex-direction: column;
                gap: 1rem;

                .contact-card {
                    padding: 16px 20px;
                }
            }
        }

        .contact-right {
            form {
                gap: 1.5rem;

                input, textarea {
                    padding: 15px;
                    min-height: 52px;
                    font-size: 16px;
                }

                textarea {
                    min-height: 110px;
                }

                .primary {
                    min-height: 52px;
                }
            }
        }
    }
}

@media (max-width: 768px) {

    .contact-container{
        padding: 12px;
        border-radius: 0;

        .curves-top {
            width: 80%;
            max-width: none;
            opacity: 0.4;
            z-index: 1;
            transform: scale(1.1);
            top: -10px;
            right: -30px;
        }

        .curves-bottom {
            width: 80%;
            max-width: none;
            opacity: 0.4;
            z-index: 1;
            transform: scale(1.1);
            bottom: -10px;
            left: -30px;
        }
        .contact-wrapper {
            grid-template-columns: 1fr;
            padding: 12px;
            .contact-left {
                gap: 1rem;

                .contact-header{
                    width: 55%;
                    h1{
                        font-size: 24px;


                    }
                }
                .contact-cards{
                    flex-direction: column;
                    width: 55%;
                }

            }
            .contact-right{

                width: 55%;
                form{
                    gap: 1rem;

                }
            }


        }
    }

}