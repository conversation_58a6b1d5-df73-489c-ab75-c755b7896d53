import Contact from '@/components/common/form/Contact'
import { Ellipsis } from 'lucide-react'
import React from 'react'

const CoursePage = () => {
    return (
        <div className='course'>
            <div className='hero container'>
                <div className='hero-text'>
                <Ellipsis color="#2E3192" />
                    <h5>State-Approved CNA Training in Shawnee, OK</h5>
                    <h1>Our Nurse Aide Training Program is a 76-hour course approved by the Oklahoma State Department of Health. </h1>
                    <p>The course covers essential skills in long-term care, personal hygiene, safety, and communication. Upon successful completion, students are eligible to take the state CNA certification exam and begin working in healthcare settings across Oklahoma.</p>
                </div>
                <div className='hero-img'>
                    <img src={'/training-img.jpg'} alt="hero" />
                </div>

            </div>
            <div className='hands-on container'>
                <img src="/curves-top.png" alt="" className="curves-top" />
                <div className='hands-on-text'>
                <Ellipsis color="#39B54A" />
                    <h5>Clinical Experience</h5>
                    <h1>Hands on CNA Training in shawnee</h1>
                </div>
                <div className='hands-on-img'>
                    <img src={'/training-img.jpg'} alt="hands-on" />

                    <div className='texts'>
                    <div className='text-top-left'>
                        <p>Students will complete 16 hours of hands-on clinical training at a state-approved healthcare facility.</p>
                    </div>
                    <div className='text-bottom-right'>
                        <p>Clinicals occur during the final week of the program and provide practical experience under supervision.</p>
                    </div>
                    </div>



                </div>
                <img src="/curves-bottom.png" alt="" className="curves-bottom" />
            </div>
            <div className='what-you-learn container'>
                <div className='what-you-learn-text'>
                <Ellipsis color="#39B54A" />
                    <h5>Curriculum Overview</h5>
                    <h1>What You’ll Learn</h1>
                    <p>Topics include:</p>
                </div>
                <div className='cards'>
                    <div className='card'>
                        <p>Communication and interpersonal skills</p>
                    </div>
                    <div className='card'>
                       <p>Infection control</p>
                    </div>
                    <div className='card'>
                        <p>Vital signs, lifting, and transfers</p>
                    </div>
                    <div className='card'>
                        <p>Safety procedures and emergency care</p>
                    </div>
                    <div className='card'>
                        <p>Residents’ rights and promoting independence</p>
                    </div>
                    <div className='card'>
                       <p>Bathing, grooming, dressing, toileting, and nutrition</p>
                    </div>
                    <div className='card'>
                        <p>Alzheimer’s and dementia care (including 10-hour requirement)</p>
                    </div>
                    <div className='card'>
                        <p>End-of-life care and postmortem procedures</p>
                    </div>

                </div>
            </div>

            <div className='FAQ container'>
                <div className='FAQ-text'>
                <Ellipsis color="#2E3192" />
                    <h5>Frequently Asked Questions</h5>
                    <h3>Have questions about our program? We have answers.</h3>
                </div>
                <div className='FAQ-cards'>
                    <div className='FAQ-card'>
                        <h4>How long is the program?</h4>
                        <p>The program is 76 hours long and typically takes 2-3 weeks to complete.</p>
                    </div>
                    <div className='FAQ-card'>
                        <h4>What are the prerequisites for enrollment?</h4>
                        <p>There are no prerequisites for enrollment. However, students must be at least 18 years old and have a high school diploma or GED.</p>
                    </div>
                    <div className='FAQ-card'>
                        <h4>How much does the program cost?</h4>
                        <p>The total cost of the program is $499, which includes classroom instruction, lab training, and clinical supervision.</p>
                    </div>
                    <div className='FAQ-card'>
                        <h4>What is the payment plan?</h4>
                        <p>A non-refundable deposit is required to secure your seat. Payment plans may be available. Contact us for details.</p>
                    </div>
                </div>
            </div>
            <Contact />
        </div>
    )
}

export default CoursePage