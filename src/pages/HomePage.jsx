'use client'
import Contact from '@/components/common/form/Contact'
import { Calendar03Icon, Calendar01Icon, MoreHorizontalIcon, FileTextIcon, Mail01Icon, Location01Icon, Note01Icon, SmartPhone01Icon, Upload01Icon, Tick02Icon } from '@hugeicons/react'
import React, { useState } from 'react'
import Link from 'next/link';
import EnrollButton from '@/components/common/EnrollButton';
import Button from '@/components/common/Button';

const HomePage = () => {
  const [email, setEmail] = useState('')

  const handleEmailChange = (e) => {
    setEmail(e.target.value)
  }

  const handleEmailSubmit = () => {

    console.log('Email submitted:', email)
  }

  return (
    <div className='home'>
      <div className='hero container'>
        <div className='left-section'>
          <div className='hero-text'>
            <h5>Welcome to Cohesive Healthcare Training Academy</h5>
            <h1>Start Your <span className='highlight'>Healthcare</span> <br /> <span className='highlight'>Journey</span> Here.</h1>
            <p>Our state-approved Nurse Aide Training Program offers the knowledge and hands-on experience needed to become a Certified Nurse Aide <br /> (CNA) in Oklahoma. </p>
            <div className='enrollment'>
              <div className='form-group'>
                <label>Email</label>
                <input
                  type='email'
                  placeholder='Enter your email'
                  value={email}
                  onChange={handleEmailChange}
                  className='enrol-input'
                />
              </div>
              <EnrollButton onClick={handleEmailSubmit} />
            </div>


          </div>
          <div className='convenient'>
            <div className='convenient-icon'>
              <div className='location'>
                <Location01Icon color="white" />
              </div>
              <div className='arrow'>
                <ArrowRight02Icon color="#2E3192" />
              </div>
            </div>
            <div className='convenient-text'>
              <p>Conveniently <br /> located in Shawnee</p>
            </div>
          </div>
        </div>

        <div className='hero-img'>
          <img src={'/hero-home.jpg'} alt="hero" />
          <div className='convenient'>
            <div className='convenient-icon'>
              <div className='location'>
                <Note01Icon color="white" />
              </div>
              <div className='arrow'>
                <ArrowRight02Icon color="#2E3192" />
              </div>
            </div>
            <div className='convenient-text'>
              <p>Next start date </p>
              <p className='date'>May 5, 2025</p>
            </div>
          </div>
        </div>
      </div>


      <div className='about-trainings'>
        <img src="/curves-top.png" alt="" className="curves-top" />

        <div className="white-card">
          <div className='about-trainings-text'>
            <div className="dots">
              <MoreHorizontalIcon color="#2E3192" />
            </div>
            <h5>State-Approved CNA Training in Shawnee, OK</h5>
            <h1>Our Nurse Aide Training Program is a 76- <br />hour course approved by the Oklahoma <br /> State Department of Health.</h1>
            <p>The course covers essential skills in long-term care, personal hygiene, safety, and communication. Upon successful completion, students are eligible to take the state CNA certification exam and begin working in healthcare settings across Oklahoma.</p>
            <Button
              text="Learn more"
              icon={<ArrowRight01Icon size={16} />}
              className="primary"
            />
          </div>
          <div className='about-trainings-img'>
            <img src={'/about.jpg'} alt="about" />
          </div>
        </div>

        <div className='class-section'>
          <div className='section-header'>
            <div className="dots green">
              <Ellipsis color="#39B54A" />
            </div>
            <h5>Classes</h5>
            <h2>Evening Class Option for <br /> Working Adults</h2>
          </div>

          <div className="class-cards">
            <div className='card-row'>
              <div className='class-card'>
                <div className="card-content">
                  <p>Total Hours:</p>
                  <h3>76 (40 classroom, 20 lab, 16 clinical)</h3>
                </div>
                <div className='card-icon'>
                  <Clock color="#39B54A" size={24} />
                </div>
              </div>

              <div className='class-card'>
                <div className="card-content">
                  <p>Course Length</p>
                  <h3>2.5 weeks</h3>
                </div>
                <div className='card-icon'>
                  <FileText color="#39B54A" size={24} />
                </div>
              </div>
            </div>

            <div className='card-row'>
              <div className='class-card'>
                <div className="card-content">
                  <p>Classroom/Lab</p>
                  <h3>4:30 PM to 9:00 PM</h3>
                </div>
                <div className='card-icon'>
                  <BookOpen color="#39B54A" size={24} />
                </div>
              </div>

              <div className='class-card'>
                <div className="card-content">
                  <p>Clinical Days</p>
                  <h3>5:00 PM to 9:00 PM (two days total)</h3>
                </div>
                <div className='card-icon'>
                  <BookOpen color="#39B54A" size={24} />
                </div>
              </div>
            </div>

            <div className='class-card full-width'>
              <div className="card-content">
                <p>Clinical Days</p>
                <h3>09:00 AM to 04:00 PM (Saturday)</h3>
              </div>
              <div className='card-icon'>
                {/* <BookOpen01Icon color="#39B54A" size={24} /> */}
              </div>
            </div>

            <div className='class-card full-width'>
              <div className="card-content">
                <p>Location</p>
                <h3>Classroom held in Shawnee; Clinicals in Oklahoma City</h3>
              </div>
              <div className='card-icon'>
                <Location01Icon color="#39B54A" size={24} />
              </div>
            </div>
          </div>

          <p className="schedule-note">
            This schedule is designed to accommodate<br />
            students who may have daytime commitments.
          </p>
        </div>

        <img src="/curves-bottom.png" alt="" className="curves-bottom" />
      </div>


      <div className='How-to-apply container'>

        <div className='apply-text'>
          <div className='left'>
            <MoreHorizontalIcon color="#39B54A" />

            <h5>Start Your Journey in Healthcare</h5>
            <h1>We make it easy to get started with <br /> our CNA program</h1>

          </div>
          <div className='right'>
            <p>Simply complete the application process and meet the required prerequisites to secure your seat in an upcoming class. </p>
            <div className='card'>
              <p>Space is limited, and enrollment is accepted on a first-come, first-served basis.</p>
            </div>
          </div>
        </div>
       <div className='How-to-apply container'>

        <div className='apply-text'>
          <div className='left'>
            <MoreHorizontalIcon color="#39B54A" />

            <h5>Start Your Journey in Healthcare</h5>
            <h1>We make it easy to get started with <br /> our CNA program</h1>

          </div>
          <div className='right'>
            <p>Simply complete the application process and meet the required prerequisites to secure your seat in an upcoming class. </p>
            <div className='card'>
              <p>Space is limited, and enrollment is accepted on a first-come, first-served basis.</p>
            </div>
          </div>
        </div>
        <div className='how-to-apply-steps'>
          <h3>How to Apply</h3>
          <div className='apply-steps'>

            <div className='step'>
              <div className='step-icon'>

              <div className='step-count'>
                <h4>STEP</h4>
                <h1>01</h1>
              </div>
               <FileTextIcon  color="#39B54A" size={32} />
              </div>
              <div className='step-text'>

                <p>Fill out and submit the CNA Training Program Application</p>        </div>
            </div>
            <div className='step'>
              <div className='step-icon'>
              <div className='step-count'>
                <h4>STEP</h4>
                <h1>02</h1>
              </div>
               <Upload01Icon  color="#39B54A" size={32} />
               </div>
              <div className='step-text'>

                <p>Submit required documentation (see prerequisites below)</p>
              </div>
            </div>
            <div className='step'>
              <div className='step-icon'>
              <div className='step-count'>
                <h4>STEP</h4>
                <h1>03</h1>
              </div>
              <Calendar01Icon  color="#39B54A" size={32} />
              </div>
              <div className='step-text'>

                <p>Schedule an admissions interview (if applicable)</p>
              </div>
            </div>
            <div className='step'>
              <div className='step-icon'>
              <div className='step-count'>
                <h4>STEP</h4>
                <h1>04</h1>

              </div>
              {/* <Wallet01Icon  color="#39B54A" size={32} /> */}
              </div>
              <div className='step-text'>

                <p>Pay any required fees or secure your payment plan</p>
              </div>
            </div>
            <div className='step'>
              <div className='step-icon'>
              <div className='step-count'>
                <h4>STEP</h4>
                <h1>05</h1>

              </div>
              <Tick02Icon  color="#39B54A" size={32} />
               </div>
              <div className='step-text'>

                <p>Receive enrollment confirmation and orientation details</p>
              </div>

            </div>
          </div>
        </div>

      </div>

      </div>
      <div className='classes'>
        <div className='classes-img'>
          <img src={'/classes.jpg'} alt="classes" />
        </div>
        <div className='classes-card'>
          <MoreHorizontalIcon color="#39B54A" />
          <h5>Classes</h5>
          <h1>Upcoming Classes Dates</h1>
          <div className='date-cards'>
            <div className='date-card'>
              <Calendar01Icon className='icon' />
              <div className='date-text'>
                <p>Next Session Begins:</p>
                <h4>May 5, 2025</h4>
              </div>

            </div>
            <div className='date-card'>
              <Calendar03Icon className='icon' />
              <div className='date-text'>
                <p>Enrollment Deadline</p>
                <h4>May 5, 2025</h4>
              </div>
            </div>

          </div>
          <p>Classes are offered monthly with rolling enrollment. Classes are filled on a first-come, first-served basis. Submit your application and deposit early to guarantee your spot.
          </p>
          <EnrollButton />

        </div>
      </div>

      <div className='tuition-payments container'>
        <div className='tuition-payments-img'>
          <img src={'/tuition.jpg'} alt="tuition" />
        </div>
        <div className='tuition-payments-text'>
          <MoreHorizontalIcon color="#39B54A" />
          <h5>Tuition & Payments</h5>
          <h1>Invest in Your Future</h1>
          <p>The total cost of the program is **$499**, which includes classroom instruction, lab training, and clinical supervision.</p>

          <div className='card deposit-note'>
            <p>💳 A non-refundable deposit is required to secure your seat</p>
          </div>
          <p>Students must fulfill all financial obligations to the program before taking the CNA state exam or receiving a Certificate of Completion.</p>
          <h4>Payment plans may be available. Contact us for details.*</h4>
          <EnrollButton />

        </div>
      </div>

      <div className='meet-instructor'>
        <img src="/curves-top.png" alt="" className="curves-top" />
        <div className='meet-instructor-img'>
          <img src={'/Kathy Small (1) 1.jpg'} alt="instructor" />
        </div>
        <div className='meet-instructor-text'>
          <MoreHorizontalIcon color="#2E3192" />
          <h5>Meet the Instructor</h5>
          <h1>Kathy Hammons, RN</h1>
          <h4>Lead Nurse Instructor</h4>
          <p>Kathy brings years of experience in nursing and healthcare education. She is passionate about preparing students to deliver compassionate, high-quality care in long-term care settings.</p>
          <div className='contact-cards'>
            <div className='contact-card'>
              <Mail01Icon color="#2E3192" />
              <div className='card-text'>
                <h4>Email</h4>
                <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
              </div>
            </div>
            <div className='contact-card'>
              <SmartPhone01Icon color="#2E3192" />
              <div className='card-text'>
                <h4>Phone Number</h4>
                <p>(*************</p>
              </div>
            </div>


          </div>
        </div>
        <img src="/curves-bottom.png" alt="" className="curves-bottom" />
      </div>

      <div className='choose-us container'>
        <div className='choose-us-text'>
          <MoreHorizontalIcon color="#39B54A" />
          <h5>Why Choose Us</h5>
          <h2>At Cohesive Healthcare Training Academy, we are committed to providing a <br /> supportive, high-quality learning experience designed to help every student <br /> succeed. Here’s what sets us apart:</h2>

        </div>
        <div className='choose-us-content'>
          <div className='card-group'>
            <div className='card-text'>
              <div className='card'></div>
              <p>Small class sizes for personalized instruction</p>
            </div>
            <div className='card-text'>
              <div className='card'></div>
              <p>Hands-on training and real-world preparation</p>
            </div>
          </div>
          <div className='card-group'>
            <div className='card-text'>
              <div className='card'></div>
              <p>Evening classes for working adults</p>
            </div>
            <div className='card-text'>
              <div className='card'></div>
              <p>Convenient Shawnee location with modern facilities</p>
            </div>
          </div>

          <div className='card-group'>
            <div className='card-text'>
              <div className='card'></div>
              <p>Experienced, compassionate instructors</p>
            </div>
            <div className='card-text'>
              <div className='card'></div>
              <p>Direct support from enrollment to certification</p>
            </div>
          </div>
        </div>
        <Link href="/courses">
          <button className="primary">
            Learn more
            <ArrowRight01Icon size={16} />
          </button>
        </Link>

      </div>

      <Contact />

    </div>
  )

}

export default HomePage
